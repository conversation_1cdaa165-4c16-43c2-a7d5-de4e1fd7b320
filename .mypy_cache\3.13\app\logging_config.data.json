{".class": "MypyFile", "_fullname": "app.logging_config", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "ENVIRONMENT": {".class": "SymbolTableNode", "cross_ref": "app.config.ENVIRONMENT", "kind": "Gdef", "module_public": false}, "SecurityFormatter": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["logging.Formatter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.logging_config.SecurityFormatter", "name": "SecurityFormatter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "app.logging_config.SecurityFormatter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.logging_config", "mro": ["app.logging_config.SecurityFormatter", "logging.Formatter", "builtins.object"], "names": {".class": "SymbolTable", "format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.logging_config.SecurityFormatter.format", "name": "format", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.logging_config.SecurityFormatter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.logging_config.SecurityFormatter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SecurityMonitor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.logging_config.SecurityMonitor", "name": "SecurityMonitor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "app.logging_config.SecurityMonitor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.logging_config", "mro": ["app.logging_config.SecurityMonitor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.logging_config.SecurityMonitor.__init__", "name": "__init__", "type": null}}, "failed_login_attempts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "app.logging_config.SecurityMonitor.failed_login_attempts", "name": "failed_login_attempts", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "is_suspicious_ip": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ip_address"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.logging_config.SecurityMonitor.is_suspicious_ip", "name": "is_suspicious_ip", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ip_address"], "arg_types": ["app.logging_config.SecurityMonitor", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_suspicious_ip of SecurityMonitor", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mark_suspicious_ip": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ip_address"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.logging_config.SecurityMonitor.mark_suspicious_ip", "name": "mark_suspicious_ip", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ip_address"], "arg_types": ["app.logging_config.SecurityMonitor", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mark_suspicious_ip of SecurityMonitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rate_limit_violations": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "app.logging_config.SecurityMonitor.rate_limit_violations", "name": "rate_limit_violations", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "suspicious_ips": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "app.logging_config.SecurityMonitor.suspicious_ips", "name": "suspicious_ips", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "track_failed_login": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "ip_address", "email"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.logging_config.SecurityMonitor.track_failed_login", "name": "track_failed_login", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "ip_address", "email"], "arg_types": ["app.logging_config.SecurityMonitor", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "track_failed_login of SecurityMonitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "track_rate_limit_violation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "ip_address", "endpoint"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.logging_config.SecurityMonitor.track_rate_limit_violation", "name": "track_rate_limit_violation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "ip_address", "endpoint"], "arg_types": ["app.logging_config.SecurityMonitor", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "track_rate_limit_violation of SecurityMonitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.logging_config.SecurityMonitor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.logging_config.SecurityMonitor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.logging_config.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.logging_config.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.logging_config.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.logging_config.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.logging_config.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.logging_config.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.logging_config.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "app_logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.logging_config.app_logger", "name": "app_logger", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef", "module_public": false}, "is_production": {".class": "SymbolTableNode", "cross_ref": "app.config.is_production", "kind": "Gdef", "module_public": false}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef", "module_public": false}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "security_logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.logging_config.security_logger", "name": "security_logger", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "security_monitor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.logging_config.security_monitor", "name": "security_monitor", "type": "app.logging_config.SecurityMonitor"}}, "setup_application_logging": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.logging_config.setup_application_logging", "name": "setup_application_logging", "type": null}}, "setup_security_logging": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.logging_config.setup_security_logging", "name": "setup_security_logging", "type": null}}}, "path": "C:\\Local\\Projects\\BioCleaning\\app\\logging_config.py"}