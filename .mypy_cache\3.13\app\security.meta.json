{"data_mtime": 1751458448, "dep_lines": [14, 18, 196, 7, 8, 9, 10, 11, 12, 13, 15, 16, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 20, 10, 10, 10, 10, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["fastapi.security", "app.config", "app.logging_config", "os", "html", "<PERSON><PERSON><PERSON>", "secrets", "typing", "datetime", "<PERSON><PERSON><PERSON>", "pydantic", "re", "builtins", "_collections_abc", "_frozen_importlib", "abc", "enum", "starlette", "starlette.requests", "types"], "hash": "cd7ef0eaad9a1c76cba9f339d370f5cb685aaa1f", "id": "app.security", "ignore_all": false, "interface_hash": "64ac1e0966ca012d4a350a689b5c4919376e4fef", "mtime": 1751458446, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Local\\Projects\\BioCleaning\\app\\security.py", "plugin_data": null, "size": 9555, "suppressed": [], "version_id": "1.15.0"}