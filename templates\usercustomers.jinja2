{% extends "base.jinja2" %}

{% block title %}Customers{% endblock %}
{% block content %}

<div>
  <style>
      me {
          position: relative;
          width: 100%;
      }
  </style>

  {# X BUTTON (TOP RIGHT) #}
    <div onclick="location.href='/calculations'">
      <style>
        me {
            position: absolute;
            top: -30px;
            right: clamp(-150px, -31%, -18px);
            width: 32px;
            height: 32px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }

          me .close-x {
              width: 26px;
              height: 2px;
              background-color: var(--color-text-dark);
              position: relative;
              transform: rotate(45deg);
              transition: all 0.3s ease;
          }

          me .close-x::after {
              content: '';
              position: absolute;
              width: 26px;
              height: 2px;
              background-color: var(--color-text-dark);
              left: 0;
              top: 0;
              transform: rotate(-90deg);
              transition: all 0.3s ease;
          }

          me:hover .close-x,
          me:hover .close-x::after {
              background-color: greenyellow;
          }
      </style>
      <div class="close-x"></div>
    </div>

    {# TITLE DOUBLE #}
    <div>
      <style>
          me {
            width: 100%;
            text-align: center;
            padding-bottom: 8px;
          }

          me hr {
            display: block;
            height: 1px;
            border: 0;
            border-top: 1px solid var(--color-hr-lines);
            margin-top: 10;
            padding: 0;
          }
      </style>
      <div>
        Customers
        <style>
            me {
              color: var(--color-text-title);
              padding-bottom: 28px;
              font-family: 'Noto Serif', serif;
              font-size: 30px;
              font-weight: 400;
              font-stretch: semi-condensed;
              font-style: italic;
            }
        </style>
      </div>
      <div>
        <style>
            me {
              color: var(--color-text-subtitle);
              padding-bottom: 2px;
              font-family: "Noto Sans", sans-serif;
              font-size: 20px;
              font-weight: 300;
              font-stretch: semi-condensed;
              font-style: normal;
            }
        </style>
        This info will be showed on the pdf report.
      </div>
      <hr />
    </div>

    <div>
      <style>
        me {
          padding-bottom: 40px;
        }
        me ul {
          margin: 0;
          padding: 0;
        }
        me ul li {
          background-color: var(--color-list-background);
          list-style: none;
          border-radius: 8px;
          margin: 0px;
          margin-top: 20px;
          padding: 0 10px;
          border: 1px solid var(--color-input-lines);
        }
        me ul li > label {
          display: flex;
          align-items: center;
          padding: 4px 10px;
          cursor: default;
          position: relative;
          font-family: 'Noto Sans', sans-serif;
          font-weight: 300;
          font-stretch: normal;
          transition: font-weight 0.4s, font-stretch 0.5s;
        }
        me ul li > label::before {
          content: '+';
          margin-right: 10px;
          font-size: 24px;
          font-weight: 600;
          width: 14px;
          text-align: center;
          transition: all 0.5s;
          display: inline-block;
          transform: rotate(0deg);
          line-height: 20px;
        }
        me ul li input[type="radio"] {
          display: none;
        }
        me ul li .acc-content {
          line-height: 26px;
          padding: 0 10px;
          max-height: 0;
          overflow: hidden;
          transition: max-height 0.8s, padding 0.8s;
          font-weight: 300;
          font-style: normal;
        }
        me ul li input[type="radio"]:checked + label + .acc-content {
          max-height: 538px;
          padding: 10px 10px;
        }
        me ul li input[type="radio"]:checked + label::before {
          content: '-';
          font-weight: 200;
          transform: rotate(180deg);
          position: relative;
          top: 4px;
        }
      </style>
      <ul>
        {% for customer in customers %}
        <li>
          <input type="radio" name="accordion" id="customer_{{ customer.id }}">
          <label for="customer_{{ customer.id }}">{{ customer.name }}</label>
          <div class="acc-content">
            {{ render_partial('partials/customer-li-item.jinja2',
               namealwayschange='customer_' + customer.id|string,
               customer_id=customer.id,
               customer_name=customer.name,
               customer_info=customer.info) }}
          </div>
        </li>
        {% endfor %}

        {% if add_new %}
        <li>
          <input type="radio" name="accordion" id="customer_new" checked>
          <label for="customer_new">New Customer</label>
          <div class="acc-content">
            {{ render_partial('partials/customer-li-item.jinja2',
               namealwayschange='customer_new',
               customer_id='',
               customer_name='',
               customer_info='') }}
          </div>
        </li>
        {% endif %}
      </ul>
    </div>

  <div id="errordiv"></div>

  {# + BUTTON Bottom RIGHT) #}
  <div {% if not add_new %}data-on-click="@get('/add_customer')"{% endif %}>
    <style>
      me {
          position: relative;
          top: -16px;
          left: auto;
          right: 0;
          margin-left: auto;
          width: 32px;
          height: 32px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 10;
      }

        me .add_customer {
            width: 26px;
            height: 2px;
            background-color: var(--color-text-dark);
            position: relative;
            transform: rotate(0deg);
            transition: all 0.3s ease;
        }

        me .add_customer::after {
            content: '';
            position: absolute;
            width: 26px;
            height: 2px;
            background-color: var(--color-text-dark);
            left: 0;
            top: 0;
            transform: rotate(90deg);
            transition: all 0.3s ease;
        }

        {% if not add_new %}
      me:hover .add_customer,
        me:hover .add_customer::after {
            background-color: greenyellow;
        }
      {% else %}
      me {
          opacity: 0.5;
          cursor: not-allowed;
      }
      me .add_customer,
      me .add_customer::after {
          background-color: var(--color-disabled);
      }
      {% endif %}
    </style>
    <div class="add_customer"></div>
  </div>

</div>

{% endblock content %}

