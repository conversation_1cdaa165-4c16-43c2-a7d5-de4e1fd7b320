{"data_mtime": 1751457943, "dep_lines": [10, 11, 12, 1, 3, 4, 5, 1, 1, 1, 1, 8, 7], "dep_prios": [5, 5, 5, 5, 10, 10, 5, 5, 30, 30, 30, 5, 10], "dependencies": ["starlette.datastructures", "starlette.requests", "starlette.types", "__future__", "json", "typing", "base64", "builtins", "_collections_abc", "_frozen_importlib", "abc"], "hash": "f7c29901b2bed3673f8290a1d1ead3f90537da89", "id": "starlette.middleware.sessions", "ignore_all": true, "interface_hash": "a966a1d3fd83fcdf8b45320564ff6c368da5e2c8", "mtime": 1741679702, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Local\\Projects\\BioCleaning\\.venv\\Lib\\site-packages\\starlette\\middleware\\sessions.py", "plugin_data": null, "size": 3566, "suppressed": ["itsdangerous.exc", "itsdangerous"], "version_id": "1.15.0"}