{".class": "MypyFile", "_fullname": "starlette.middleware.sessions", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ASGIApp": {".class": "SymbolTableNode", "cross_ref": "starlette.types.ASGIApp", "kind": "Gdef"}, "BadSignature": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "starlette.middleware.sessions.BadSignature", "name": "BadSignature", "type": {".class": "AnyType", "missing_import_name": "starlette.middleware.sessions.BadSignature", "source_any": null, "type_of_any": 3}}}, "HTTPConnection": {".class": "SymbolTableNode", "cross_ref": "starlette.requests.HTTPConnection", "kind": "Gdef"}, "Message": {".class": "SymbolTableNode", "cross_ref": "starlette.types.Message", "kind": "Gdef"}, "MutableHeaders": {".class": "SymbolTableNode", "cross_ref": "starlette.datastructures.MutableHeaders", "kind": "Gdef"}, "Receive": {".class": "SymbolTableNode", "cross_ref": "starlette.types.Receive", "kind": "Gdef"}, "Scope": {".class": "SymbolTableNode", "cross_ref": "starlette.types.Scope", "kind": "Gdef"}, "Secret": {".class": "SymbolTableNode", "cross_ref": "starlette.datastructures.Secret", "kind": "Gdef"}, "Send": {".class": "SymbolTableNode", "cross_ref": "starlette.types.Send", "kind": "Gdef"}, "SessionMiddleware": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette.middleware.sessions.SessionMiddleware", "name": "SessionMiddleware", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "starlette.middleware.sessions.SessionMiddleware", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "starlette.middleware.sessions", "mro": ["starlette.middleware.sessions.SessionMiddleware", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "scope", "receive", "send"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "starlette.middleware.sessions.SessionMiddleware.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "scope", "receive", "send"], "arg_types": ["starlette.middleware.sessions.SessionMiddleware", {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Scope"}, {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Receive"}, {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Send"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of SessionMiddleware", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "app", "secret_key", "session_cookie", "max_age", "path", "same_site", "https_only", "domain"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "starlette.middleware.sessions.SessionMiddleware.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "app", "secret_key", "session_cookie", "max_age", "path", "same_site", "https_only", "domain"], "arg_types": ["starlette.middleware.sessions.SessionMiddleware", {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.ASGIApp"}, {".class": "UnionType", "items": ["builtins.str", "starlette.datastructures.Secret"], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "lax"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "strict"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "none"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SessionMiddleware", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "app": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.middleware.sessions.SessionMiddleware.app", "name": "app", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Scope"}, {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Receive"}, {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Send"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "max_age": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.middleware.sessions.SessionMiddleware.max_age", "name": "max_age", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.middleware.sessions.SessionMiddleware.path", "name": "path", "type": "builtins.str"}}, "security_flags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.middleware.sessions.SessionMiddleware.security_flags", "name": "security_flags", "type": "builtins.str"}}, "session_cookie": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.middleware.sessions.SessionMiddleware.session_cookie", "name": "session_cookie", "type": "builtins.str"}}, "signer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.middleware.sessions.SessionMiddleware.signer", "name": "signer", "type": {".class": "AnyType", "missing_import_name": "starlette.middleware.sessions.itsdangerous", "source_any": {".class": "AnyType", "missing_import_name": "starlette.middleware.sessions.itsdangerous", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.middleware.sessions.SessionMiddleware.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "starlette.middleware.sessions.SessionMiddleware", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.middleware.sessions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.middleware.sessions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.middleware.sessions.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.middleware.sessions.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.middleware.sessions.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.middleware.sessions.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "b64decode": {".class": "SymbolTableNode", "cross_ref": "base64.b64decode", "kind": "Gdef"}, "b64encode": {".class": "SymbolTableNode", "cross_ref": "base64.b64encode", "kind": "Gdef"}, "itsdangerous": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "starlette.middleware.sessions.itsdangerous", "name": "itsdangerous", "type": {".class": "AnyType", "missing_import_name": "starlette.middleware.sessions.itsdangerous", "source_any": null, "type_of_any": 3}}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "c:\\Local\\Projects\\BioCleaning\\.venv\\Lib\\site-packages\\starlette\\middleware\\sessions.py"}